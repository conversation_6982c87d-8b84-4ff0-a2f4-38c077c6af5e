#!/usr/bin/env python3
"""
Test script to validate MCP configuration for Augment integration
"""

import json
import os
import sys
from pathlib import Path

def test_mcp_config():
    """Test the MCP configuration files"""
    
    print("🔍 Testing Gitea MCP Configuration for Augment...")
    print("=" * 50)
    
    # Test 1: Check if configuration files exist
    config_files = [
        "gitea-mcp-workflow.json",
        "gitea-mcp-enhanced.json"
    ]
    
    for config_file in config_files:
        if Path(config_file).exists():
            print(f"✅ {config_file} exists")
            
            # Validate JSON syntax
            try:
                with open(config_file, 'r') as f:
                    config = json.load(f)
                print(f"✅ {config_file} has valid JSON syntax")
                
                # Check MCP structure
                if "mcpServers" in config:
                    print(f"✅ {config_file} has proper MCP structure")
                    
                    # List servers
                    for server_name, server_config in config["mcpServers"].items():
                        print(f"   📡 Server: {server_name}")
                        print(f"      Command: {server_config.get('command', 'N/A')}")
                        print(f"      Args: {server_config.get('args', [])}")
                        
                        # Check environment variables
                        env_vars = server_config.get('env', {})
                        for env_key, env_value in env_vars.items():
                            status = "⚠️  NOT SET" if not env_value else "✅ SET"
                            print(f"      {env_key}: {status}")
                else:
                    print(f"❌ {config_file} missing 'mcpServers' key")
                    
            except json.JSONDecodeError as e:
                print(f"❌ {config_file} has invalid JSON: {e}")
        else:
            print(f"❌ {config_file} not found")
    
    print("\n" + "=" * 50)
    
    # Test 2: Check if MCP server file exists
    mcp_server_path = Path("mcp/server.py")
    if mcp_server_path.exists():
        print("✅ MCP server file (mcp/server.py) exists")
    else:
        print("❌ MCP server file (mcp/server.py) not found")
        print("   You need to create the MCP server implementation")
    
    # Test 3: Check environment variables
    print("\n🌍 Environment Variables:")
    required_env_vars = ["GITEA_URL", "GITEA_ACCESS_TOKEN"]
    
    for env_var in required_env_vars:
        value = os.getenv(env_var)
        if value:
            # Don't print the actual token for security
            display_value = value if env_var != "GITEA_ACCESS_TOKEN" else "***HIDDEN***"
            print(f"✅ {env_var}: {display_value}")
        else:
            print(f"⚠️  {env_var}: Not set")
    
    # Test 4: Check working directory
    print(f"\n📁 Current working directory: {os.getcwd()}")
    expected_dir = "/home/<USER>/Documents/TEST_PROJECTS/gitea-mcp-ide"
    if os.getcwd() == expected_dir:
        print("✅ Working directory is correct")
    else:
        print(f"⚠️  Expected working directory: {expected_dir}")
    
    print("\n" + "=" * 50)
    print("🎯 Next Steps:")
    print("1. Set GITEA_ACCESS_TOKEN environment variable")
    print("2. Import configuration into Augment:")
    print("   claude mcp add-json gitea-mcp-basic '<json_config>'")
    print("3. Test MCP connection in Augment")
    print("=" * 50)

if __name__ == "__main__":
    test_mcp_config()
