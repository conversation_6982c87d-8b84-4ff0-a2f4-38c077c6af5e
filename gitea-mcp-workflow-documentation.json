{"name": "Gitea MCP Workflow System", "version": "1.0.0", "description": "Development workflow system that integrates with Gitea repositories via Model Context Protocol (MCP) to provide intelligent, context-aware code retrieval for Augment AI", "repository_root": "/home/<USER>/Documents/TEST_PROJECTS/gitea-mcp-ide", "augment_integration": {"description": "Augment AI uses this MCP server to automatically pull code from Gitea repositories", "transport": "stdio", "role": "Augment is the AI model that consumes MCP tools - not OpenAI or other external models", "usage": "When user requests features from Gitea repos, Augment launches MCP server and uses tools to retrieve code"}, "mcp_components": {"server": {"file": "mcp/server.py", "class": "GiteaMCPServer", "description": "MCP-compatible server providing interface to Gitea repositories", "transport": "stdio", "protocol": "MCP over stdio for Augment integration", "capabilities": ["Standard input/output communication", "JSON-RPC protocol compliance", "Multiple authentication methods", "Tool-based repository operations", "Direct process communication"]}}, "environment_variables": {"required_for_augment": {"GITEA_URL": "Gitea instance URL (default: http://localhost:3000)", "GITEA_ACCESS_TOKEN": "Personal access token for Gitea authentication"}, "optional_for_augment": {"GITEA_USERNAME": "Gitea username (alternative to access token)", "GITEA_PASSWORD": "Gitea password (alternative to access token)", "LOCAL_REPO_CLONE_PATH": "Local repository clone path", "FILE_CACHE_DIR": "File cache directory", "FILE_CACHE_MAX_AGE": "File cache maximum age in seconds"}, "not_needed_for_augment": {"GITMCP_SERVER_URL": "Not needed - Augment uses stdio transport", "GITMCP_API_KEY": "Not needed - Augment uses stdio transport", "OPENAI_API_BASE_URL": "Not needed - Augment is the AI model", "OPENAI_API_KEY": "Not needed - Augment is the AI model", "OPENAI_MODEL_NAME": "Not needed - Augment is the AI model"}}, "augment_stdio_integration": {"description": "Specific configuration for Augment to use gitea-mcp via stdio transport", "mcp_server_command": {"basic_server": {"executable": "python", "args": ["mcp/server.py"], "description": "Original basic MCP server with limited tools"}, "enhanced_server": {"executable": "python", "args": ["mcp/enhanced_server.py"], "description": "Enhanced MCP server with comprehensive search capabilities", "recommended": true}, "startup_script": {"executable": "python", "args": ["start_enhanced_mcp.py"], "description": "Startup script with environment validation and easy launch"}, "working_directory": "/home/<USER>/Documents/TEST_PROJECTS/gitea-mcp-ide", "environment_variables": {"GITEA_URL": "required", "GITEA_ACCESS_TOKEN": "required", "GITMCP_SERVER_URL": "not_needed_for_stdio", "OPENAI_API_BASE_URL": "not_needed_augment_is_the_model", "OPENAI_API_KEY": "not_needed_augment_is_the_model"}}, "protocol_flow": {"1_launch": "Start MCP server process with stdio", "2_initialize": "Send initialize request with Augment client info", "3_list_tools": "Request available tools from server", "4_call_tools": "Execute repository operations via tool calls", "5_terminate": "Clean shutdown of MCP session"}}}