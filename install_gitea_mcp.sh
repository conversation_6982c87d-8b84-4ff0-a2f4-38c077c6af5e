#!/bin/bash

# Script to install the official Gitea MCP server
# Usage: ./install_gitea_mcp.sh

echo "📦 Installing Official Gitea MCP Server..."
echo "=========================================="

# Detect architecture
ARCH=$(uname -m)
case $ARCH in
    x86_64)
        ARCH="amd64"
        ;;
    aarch64|arm64)
        ARCH="arm64"
        ;;
    *)
        echo "❌ Unsupported architecture: $ARCH"
        exit 1
        ;;
esac

# Detect OS
OS=$(uname -s | tr '[:upper:]' '[:lower:]')
case $OS in
    linux)
        OS="linux"
        ;;
    darwin)
        OS="darwin"
        ;;
    *)
        echo "❌ Unsupported OS: $OS"
        exit 1
        ;;
esac

BINARY_NAME="gitea-mcp_Linux_x86_64.tar.gz"
DOWNLOAD_URL="https://gitea.com/gitea/gitea-mcp/releases/download/v0.2.0/${BINARY_NAME}"

echo "🔍 Detected system: ${OS}-${ARCH}"
echo "📥 Download URL: ${DOWNLOAD_URL}"

# Check if gitea-mcp is already installed
if command -v gitea-mcp &> /dev/null; then
    echo "✅ Gitea MCP server already installed: $(gitea-mcp --version)"
    read -p "Do you want to reinstall? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Installation cancelled."
        exit 0
    fi
fi

# Create temporary directory
TEMP_DIR=$(mktemp -d)
cd "$TEMP_DIR"

echo "📥 Downloading Gitea MCP server..."
if command -v wget &> /dev/null; then
    wget -O "${BINARY_NAME}" "$DOWNLOAD_URL"
elif command -v curl &> /dev/null; then
    curl -L -o "${BINARY_NAME}" "$DOWNLOAD_URL"
else
    echo "❌ Neither wget nor curl found. Please install one of them."
    exit 1
fi

if [ $? -ne 0 ]; then
    echo "❌ Download failed!"
    exit 1
fi

echo "✅ Download completed"

# Extract the tar.gz file
echo "📦 Extracting archive..."
tar -xzf "${BINARY_NAME}"
if [ $? -ne 0 ]; then
    echo "❌ Extraction failed!"
    exit 1
fi

# Find the extracted binary
EXTRACTED_BINARY=$(find . -name "gitea-mcp" -type f | head -1)
if [ -z "$EXTRACTED_BINARY" ]; then
    echo "❌ Could not find gitea-mcp binary in extracted files!"
    exit 1
fi

# Make executable
chmod +x "$EXTRACTED_BINARY"

# Test the binary
echo "🧪 Testing binary..."
"$EXTRACTED_BINARY" -h > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "❌ Binary test failed!"
    exit 1
fi

echo "✅ Binary test passed"

# Install to system
echo "📦 Installing to /usr/local/bin..."
if [ "$EUID" -eq 0 ]; then
    # Running as root
    mv "$EXTRACTED_BINARY" /usr/local/bin/gitea-mcp
else
    # Not root, use sudo
    sudo mv "$EXTRACTED_BINARY" /usr/local/bin/gitea-mcp
fi

if [ $? -ne 0 ]; then
    echo "❌ Installation failed!"
    echo "💡 Try running with sudo or install manually:"
    echo "   sudo cp $EXTRACTED_BINARY /usr/local/bin/gitea-mcp"
    exit 1
fi

# Cleanup
cd - > /dev/null
rm -rf "$TEMP_DIR"

echo "✅ Installation completed successfully!"
echo "🔍 Verifying installation..."
gitea-mcp --version

echo ""
echo "🎉 Gitea MCP server is now ready!"
echo "📝 Next steps:"
echo "1. Set your GITEA_ACCESS_TOKEN environment variable"
echo "2. Run the import script: ./import_to_augment.sh"
echo "3. Test in Augment with: 'gitea mcp server version'"
