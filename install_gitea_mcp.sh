#!/bin/bash

# Script to install the official Gitea MCP server
# Usage: ./install_gitea_mcp.sh

echo "📦 Installing Official Gitea MCP Server..."
echo "=========================================="

# Detect architecture
ARCH=$(uname -m)
case $ARCH in
    x86_64)
        ARCH="amd64"
        ;;
    aarch64|arm64)
        ARCH="arm64"
        ;;
    *)
        echo "❌ Unsupported architecture: $ARCH"
        exit 1
        ;;
esac

# Detect OS
OS=$(uname -s | tr '[:upper:]' '[:lower:]')
case $OS in
    linux)
        OS="linux"
        ;;
    darwin)
        OS="darwin"
        ;;
    *)
        echo "❌ Unsupported OS: $OS"
        exit 1
        ;;
esac

BINARY_NAME="gitea-mcp-${OS}-${ARCH}"
DOWNLOAD_URL="https://gitea.com/gitea/gitea-mcp/releases/latest/download/${BINARY_NAME}"

echo "🔍 Detected system: ${OS}-${ARCH}"
echo "📥 Download URL: ${DOWNLOAD_URL}"

# Check if gitea-mcp is already installed
if command -v gitea-mcp &> /dev/null; then
    echo "✅ Gitea MCP server already installed: $(gitea-mcp --version)"
    read -p "Do you want to reinstall? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Installation cancelled."
        exit 0
    fi
fi

# Create temporary directory
TEMP_DIR=$(mktemp -d)
cd "$TEMP_DIR"

echo "📥 Downloading Gitea MCP server..."
if command -v wget &> /dev/null; then
    wget -O gitea-mcp "$DOWNLOAD_URL"
elif command -v curl &> /dev/null; then
    curl -L -o gitea-mcp "$DOWNLOAD_URL"
else
    echo "❌ Neither wget nor curl found. Please install one of them."
    exit 1
fi

if [ $? -ne 0 ]; then
    echo "❌ Download failed!"
    exit 1
fi

echo "✅ Download completed"

# Make executable
chmod +x gitea-mcp

# Test the binary
echo "🧪 Testing binary..."
./gitea-mcp --version
if [ $? -ne 0 ]; then
    echo "❌ Binary test failed!"
    exit 1
fi

echo "✅ Binary test passed"

# Install to system
echo "📦 Installing to /usr/local/bin..."
if [ "$EUID" -eq 0 ]; then
    # Running as root
    mv gitea-mcp /usr/local/bin/
else
    # Not root, use sudo
    sudo mv gitea-mcp /usr/local/bin/
fi

if [ $? -ne 0 ]; then
    echo "❌ Installation failed!"
    echo "💡 Try running with sudo or install manually:"
    echo "   cp gitea-mcp /usr/local/bin/"
    exit 1
fi

# Cleanup
cd - > /dev/null
rm -rf "$TEMP_DIR"

echo "✅ Installation completed successfully!"
echo "🔍 Verifying installation..."
gitea-mcp --version

echo ""
echo "🎉 Gitea MCP server is now ready!"
echo "📝 Next steps:"
echo "1. Set your GITEA_ACCESS_TOKEN environment variable"
echo "2. Run the import script: ./import_to_augment.sh"
echo "3. Test in Augment with: 'gitea mcp server version'"
