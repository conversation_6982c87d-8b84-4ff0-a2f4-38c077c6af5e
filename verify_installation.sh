#!/bin/bash

# Verification script for Gitea MCP installation
echo "🔍 Verifying Gitea MCP Installation..."
echo "====================================="

# Check if gitea-mcp is in PATH
if command -v gitea-mcp &> /dev/null; then
    echo "✅ gitea-mcp binary found in PATH"
    
    # Test the binary
    echo "🧪 Testing gitea-mcp binary..."
    gitea-mcp -h > /dev/null 2>&1
    if [ $? -eq 0 ]; then
        echo "✅ gitea-mcp binary is working"
    else
        echo "❌ gitea-mcp binary test failed"
    fi
else
    echo "❌ gitea-mcp not found in PATH"
    echo "💡 Checking local installation..."
    
    if [ -f "$HOME/.local/bin/gitea-mcp" ]; then
        echo "✅ Found gitea-mcp in ~/.local/bin/"
        echo "⚠️  Adding to PATH for this session..."
        export PATH="$HOME/.local/bin:$PATH"
        
        if command -v gitea-mcp &> /dev/null; then
            echo "✅ gitea-mcp now available in PATH"
        else
            echo "❌ Still can't find gitea-mcp"
        fi
    else
        echo "❌ gitea-mcp not found in ~/.local/bin/ either"
        echo "🔧 Please run: ./install_gitea_mcp.sh"
        exit 1
    fi
fi

# Check configuration files
echo ""
echo "📁 Checking configuration files..."
if [ -f "gitea-mcp-workflow.json" ]; then
    echo "✅ gitea-mcp-workflow.json exists"
    
    # Validate JSON
    python -c "import json; json.load(open('gitea-mcp-workflow.json'))" 2>/dev/null
    if [ $? -eq 0 ]; then
        echo "✅ Configuration JSON is valid"
    else
        echo "❌ Configuration JSON is invalid"
    fi
else
    echo "❌ gitea-mcp-workflow.json not found"
fi

# Test connection to Gitea (optional)
echo ""
echo "🌐 Testing Gitea connection..."
curl -s -o /dev/null -w "%{http_code}" http://localhost:3000 > /tmp/gitea_status 2>/dev/null
GITEA_STATUS=$(cat /tmp/gitea_status)

if [ "$GITEA_STATUS" = "200" ]; then
    echo "✅ Gitea server is accessible at http://localhost:3000"
elif [ "$GITEA_STATUS" = "000" ]; then
    echo "⚠️  Cannot connect to Gitea at http://localhost:3000"
    echo "   Make sure Gitea is running"
else
    echo "⚠️  Gitea responded with status: $GITEA_STATUS"
fi

rm -f /tmp/gitea_status

echo ""
echo "🎯 Summary:"
echo "==========="

if command -v gitea-mcp &> /dev/null && [ -f "gitea-mcp-workflow.json" ]; then
    echo "✅ Installation is complete and ready!"
    echo ""
    echo "📋 Import command for Augment:"
    echo "claude mcp add-json gitea-mcp '{\"command\":\"gitea-mcp\",\"args\":[\"-t\",\"stdio\",\"--host\",\"http://localhost:3000\"],\"env\":{\"GITEA_ACCESS_TOKEN\":\"MCP-Debug-Token-May20\"}}'"
    echo ""
    echo "🧪 Test in Augment with: 'gitea mcp server version'"
else
    echo "❌ Installation incomplete"
    echo "🔧 Please check the errors above and fix them"
fi

echo ""
echo "📖 See IMPORT_COMMANDS.md for detailed instructions"
