# Augment MCP Setup Guide - Official Gitea MCP Server

## Problem Solved ✅
The "Failed to save server" error occurred because you were trying to import a workflow documentation file instead of a proper MCP server configuration. I've now configured it to use the **official Gitea MCP server** from https://gitea.com/gitea/gitea-mcp with all enhanced features.

## Official Gitea MCP Server Features 🚀

The official Gitea MCP server provides comprehensive enhanced features:

### 🔧 **Repository Management**
- Create/Fork/Query repositories
- List all user repositories
- Repository search capabilities

### 📝 **Code Management**
- Branch operations (create/delete/list)
- File operations (get/create/update/delete)
- Commit history viewing

### 🤝 **Collaboration Features**
- Issue management (create/edit/comment/list)
- Pull Request workflow (create/list/get details)
- User and organization search

### 🔍 **Enhanced Search Capabilities**
- Multi-dimensional repository search
- User search across the instance
- Organization team search
- Content-based search

### 📊 **Advanced Tools**
- Release management (create/delete/list)
- Tag management (create/delete/list)
- Version information
- Debug mode support

## Configuration Files Created 📁

### 1. **Main Configuration**: `gitea-mcp-workflow.json`
```json
{
  "mcpServers": {
    "gitea-mcp": {
      "command": "gitea-mcp",
      "args": ["-t", "stdio", "--host", "http://localhost:3000"],
      "env": {"GITEA_ACCESS_TOKEN": ""}
    }
  }
}
```

### 2. **Enhanced Configuration**: `gitea-mcp-enhanced.json`
Multiple server configurations:
- **gitea-mcp-stdio**: Standard configuration
- **gitea-mcp-debug**: Debug mode enabled
- **gitea-mcp-readonly**: Read-only mode for safety
- **gitea-mcp-insecure**: For self-signed certificates

## Installation Requirements 📋

### 1. Install Official Gitea MCP Server
```bash
# Option 1: Download pre-compiled binary
wget https://gitea.com/gitea/gitea-mcp/releases/latest/download/gitea-mcp-linux-amd64
chmod +x gitea-mcp-linux-amd64
sudo mv gitea-mcp-linux-amd64 /usr/local/bin/gitea-mcp

# Option 2: Build from source
git clone https://gitea.com/gitea/gitea-mcp.git
cd gitea-mcp
make build
sudo cp gitea-mcp /usr/local/bin/
```

### 2. Verify Installation
```bash
gitea-mcp --version
```

## How to Import into Augment 🔄

### Method 1: Direct JSON Import (Recommended)
```bash
claude mcp add-json gitea-mcp '{"command":"gitea-mcp","args":["-t","stdio","--host","http://localhost:3000"],"env":{"GITEA_ACCESS_TOKEN":"your_token_here"}}'
```

### Method 2: Manual Server Addition
```bash
claude mcp add gitea-mcp -e GITEA_ACCESS_TOKEN=your_token_here -- gitea-mcp -t stdio --host http://localhost:3000
```

### Method 3: Project-scoped Configuration
```bash
claude mcp add gitea-mcp -s project -e GITEA_ACCESS_TOKEN=your_token_here -- gitea-mcp -t stdio --host http://localhost:3000
```

## Configuration Options ⚙️

### Command Line Arguments
- `-t stdio`: Use stdio transport (required for Augment)
- `--host URL`: Your Gitea instance URL
- `--token TOKEN`: Access token (alternative to env var)
- `-d`: Enable debug mode
- `--readonly`: Enable read-only mode
- `--insecure`: Allow insecure HTTPS connections

### Environment Variables
- `GITEA_ACCESS_TOKEN`: Your personal access token (required)
- `GITEA_INSECURE`: Set to "true" for self-signed certificates

## Enhanced Features Available 🎯

Once configured, you can use natural language with Augment to:

### Repository Operations
- "List all my repositories"
- "Create a new repository called 'my-project'"
- "Fork the repository owner/repo-name"
- "Search for repositories containing 'docker'"

### Code Management
- "Show me the content of README.md in my-repo"
- "Create a new branch called 'feature-branch' in my-repo"
- "List all branches in my-repo"
- "Show commit history for my-repo"

### File Operations
- "Create a new file called 'config.yaml' with content..."
- "Update the file 'main.py' in my-repo"
- "Delete the file 'old-config.json'"

### Collaboration
- "List all issues in my-repo"
- "Create a new issue titled 'Bug in login system'"
- "Show me pull request #5 in my-repo"
- "Create a pull request from feature-branch to main"

### Search & Discovery
- "Search for users named 'john'"
- "Find repositories related to 'kubernetes'"
- "List teams in organization 'my-org'"

## Testing Your Setup 🧪

### 1. Test Server Installation
```bash
gitea-mcp -t stdio --host http://localhost:3000 --token your_token_here
```

### 2. Test in Augment
After importing, try these commands:
```
gitea mcp server version
list all my repositories
search for repositories containing "test"
```

## Troubleshooting 🔧

### Common Issues
1. **"gitea-mcp command not found"**
   - Ensure the binary is in your PATH
   - Verify installation with `which gitea-mcp`

2. **"Authentication failed"**
   - Check your access token is valid
   - Verify token has required permissions

3. **"Connection refused"**
   - Confirm your Gitea instance URL is correct
   - Check if Gitea is running and accessible

### Debug Mode
Enable debug mode for troubleshooting:
```bash
claude mcp add gitea-mcp-debug -e GITEA_ACCESS_TOKEN=your_token -- gitea-mcp -t stdio --host http://localhost:3000 -d
```

## Next Steps 🎉
1. ✅ Install the official Gitea MCP server binary
2. ✅ Set your `GITEA_ACCESS_TOKEN`
3. ✅ Import configuration into Augment
4. ✅ Test with natural language commands
5. ✅ Enjoy enhanced Gitea integration with company-wide search!
