# Augment MCP Setup Guide

## Problem Solved
The "Failed to save server" error occurs when trying to import `gitea-mcp-workflow.json` because it was a workflow documentation file, not a proper MCP server configuration that Augment can import.

## Solution
I've created proper MCP server configuration files that Augment can import:

### 1. Basic Configuration: `gitea-mcp-workflow.json`
This is now a proper MCP server configuration with the basic setup:
```json
{
  "mcpServers": {
    "gitea-mcp": {
      "command": "python",
      "args": ["mcp/server.py"],
      "env": {
        "GITEA_URL": "http://localhost:3000",
        "GITEA_ACCESS_TOKEN": ""
      }
    }
  }
}
```

### 2. Enhanced Configuration: `gitea-mcp-enhanced.json`
This includes multiple server options (basic, enhanced, startup script).

## How to Import into Augment

### Method 1: Import JSON Configuration
```bash
# Import the basic configuration
claude mcp add-json gitea-mcp-basic '{"command":"python","args":["mcp/server.py"],"env":{"GITEA_URL":"http://localhost:3000","GITEA_ACCESS_TOKEN":""}}'

# Or import from file (if Augment supports file import)
claude mcp add-from-file gitea-mcp-workflow.json
```

### Method 2: Manual MCP Server Addition
```bash
# Add the MCP server manually
claude mcp add gitea-mcp -e GITEA_URL=http://localhost:3000 -e GITEA_ACCESS_TOKEN=your_token_here -- python mcp/server.py
```

### Method 3: Project-scoped Configuration
```bash
# Add as project-scoped server (shared with team)
claude mcp add gitea-mcp -s project -e GITEA_URL=http://localhost:3000 -e GITEA_ACCESS_TOKEN=your_token_here -- python mcp/server.py
```

## Required Setup Steps

### 1. Set Environment Variables
You need to set your Gitea credentials:
```bash
export GITEA_URL="http://localhost:3000"  # Your Gitea instance URL
export GITEA_ACCESS_TOKEN="your_personal_access_token"  # Your Gitea access token
```

### 2. Verify MCP Server Exists
Make sure the MCP server file exists:
```bash
ls -la mcp/server.py  # Should exist
```

### 3. Test MCP Server
Test the server works independently:
```bash
cd /home/<USER>/Documents/TEST_PROJECTS/gitea-mcp-ide
python mcp/server.py
```

## Working Directory
The MCP server should be run from:
```
/home/<USER>/Documents/TEST_PROJECTS/gitea-mcp-ide
```

## Transport Protocol
- **Transport**: stdio (standard input/output)
- **Protocol**: JSON-RPC 2.0
- **Communication**: Direct process communication via stdin/stdout

## Next Steps
1. Set your `GITEA_ACCESS_TOKEN` in the configuration
2. Import the configuration into Augment
3. Test the MCP connection
4. Use Augment to query your Gitea repositories

## Troubleshooting
- Ensure Python is available in your PATH
- Verify the `mcp/server.py` file exists and is executable
- Check that your Gitea instance is accessible
- Validate your access token has proper permissions
