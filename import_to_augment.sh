#!/bin/bash

# Script to import Official Gitea MCP Server configuration into Augment
# Usage: ./import_to_augment.sh

echo "🚀 Importing Official Gitea MCP Server into Augment..."
echo "====================================================="

# Check if gitea-mcp binary is installed
if ! command -v gitea-mcp &> /dev/null; then
    echo "❌ Official Gitea MCP server (gitea-mcp) not found in PATH!"
    echo "📥 Please install it first:"
    echo "   wget https://gitea.com/gitea/gitea-mcp/releases/latest/download/gitea-mcp-linux-amd64"
    echo "   chmod +x gitea-mcp-linux-amd64"
    echo "   sudo mv gitea-mcp-linux-amd64 /usr/local/bin/gitea-mcp"
    exit 1
fi

echo "✅ Official Gitea MCP server found: $(gitea-mcp --version)"

# Check if configuration file exists
if [ ! -f "gitea-mcp-workflow.json" ]; then
    echo "❌ Configuration file 'gitea-mcp-workflow.json' not found!"
    exit 1
fi

echo "✅ Configuration file found"

# Method 1: Try to import using add-json command
echo ""
echo "📥 Method 1: Importing using claude mcp add-json..."

# Extract the server configuration for the add-json command
SERVER_CONFIG='{"command":"gitea-mcp","args":["-t","stdio","--host","http://localhost:3000"],"env":{"GITEA_ACCESS_TOKEN":""}}'

echo "Running: claude mcp add-json gitea-mcp '$SERVER_CONFIG'"
claude mcp add-json gitea-mcp "$SERVER_CONFIG"

if [ $? -eq 0 ]; then
    echo "✅ Successfully imported using add-json method"
else
    echo "⚠️  add-json method failed, trying alternative methods..."

    # Method 2: Try manual add command
    echo ""
    echo "📥 Method 2: Importing using claude mcp add..."
    echo "Running: claude mcp add gitea-mcp -e GITEA_ACCESS_TOKEN= -- gitea-mcp -t stdio --host http://localhost:3000"
    claude mcp add gitea-mcp -e GITEA_ACCESS_TOKEN= -- gitea-mcp -t stdio --host http://localhost:3000

    if [ $? -eq 0 ]; then
        echo "✅ Successfully imported using manual add method"
    else
        echo "❌ Manual add method also failed"
        echo ""
        echo "🔧 Manual Import Instructions:"
        echo "1. Open Augment"
        echo "2. Go to MCP server settings"
        echo "3. Add a new server with these details:"
        echo "   - Name: gitea-mcp"
        echo "   - Command: gitea-mcp"
        echo "   - Args: -t stdio --host http://localhost:3000"
        echo "   - Environment Variables:"
        echo "     - GITEA_ACCESS_TOKEN: [your_token_here]"
        echo ""
        echo "📄 Or try importing this JSON directly:"
        cat gitea-mcp-workflow.json
    fi
fi

echo ""
echo "🔍 Verifying import..."
claude mcp list

echo ""
echo "✅ Import process completed!"
echo "📝 Next steps:"
echo "1. Set your GITEA_ACCESS_TOKEN in the server configuration"
echo "2. Test the MCP connection in Augment with: 'gitea mcp server version'"
echo "3. Try enhanced features:"
echo "   - 'list all my repositories'"
echo "   - 'search for repositories containing docker'"
echo "   - 'show me the content of README.md in my-repo'"
echo "   - 'create a new issue in my-repo'"
