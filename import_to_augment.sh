#!/bin/bash

# Script to import Gitea MCP configuration into Augment
# Usage: ./import_to_augment.sh

echo "🚀 Importing Gitea MCP Configuration into Augment..."
echo "=================================================="

# Check if configuration file exists
if [ ! -f "gitea-mcp-workflow.json" ]; then
    echo "❌ Configuration file 'gitea-mcp-workflow.json' not found!"
    exit 1
fi

echo "✅ Configuration file found"

# Method 1: Try to import using add-json command
echo ""
echo "📥 Method 1: Importing using claude mcp add-json..."

# Extract the server configuration for the add-json command
SERVER_CONFIG='{"command":"python","args":["mcp/server.py"],"env":{"GITEA_URL":"http://localhost:3000","GITEA_ACCESS_TOKEN":""}}'

echo "Running: claude mcp add-json gitea-mcp '$SERVER_CONFIG'"
claude mcp add-json gitea-mcp "$SERVER_CONFIG"

if [ $? -eq 0 ]; then
    echo "✅ Successfully imported using add-json method"
else
    echo "⚠️  add-json method failed, trying alternative methods..."
    
    # Method 2: Try manual add command
    echo ""
    echo "📥 Method 2: Importing using claude mcp add..."
    echo "Running: claude mcp add gitea-mcp -e GITEA_URL=http://localhost:3000 -e GITEA_ACCESS_TOKEN= -- python mcp/server.py"
    claude mcp add gitea-mcp -e GITEA_URL=http://localhost:3000 -e GITEA_ACCESS_TOKEN= -- python mcp/server.py
    
    if [ $? -eq 0 ]; then
        echo "✅ Successfully imported using manual add method"
    else
        echo "❌ Manual add method also failed"
        echo ""
        echo "🔧 Manual Import Instructions:"
        echo "1. Open Augment"
        echo "2. Go to MCP server settings"
        echo "3. Add a new server with these details:"
        echo "   - Name: gitea-mcp"
        echo "   - Command: python"
        echo "   - Args: mcp/server.py"
        echo "   - Environment Variables:"
        echo "     - GITEA_URL: http://localhost:3000"
        echo "     - GITEA_ACCESS_TOKEN: [your_token_here]"
        echo ""
        echo "📄 Or try importing this JSON directly:"
        cat gitea-mcp-workflow.json
    fi
fi

echo ""
echo "🔍 Verifying import..."
claude mcp list

echo ""
echo "✅ Import process completed!"
echo "📝 Next steps:"
echo "1. Set your GITEA_ACCESS_TOKEN in the server configuration"
echo "2. Test the MCP connection in Augment"
echo "3. Try querying your Gitea repositories"
