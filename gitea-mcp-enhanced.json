{"mcpServers": {"gitea-mcp-basic": {"command": "python", "args": ["mcp/server.py"], "env": {"GITEA_URL": "http://localhost:3000", "GITEA_ACCESS_TOKEN": ""}}, "gitea-mcp-enhanced": {"command": "python", "args": ["mcp/enhanced_server.py"], "env": {"GITEA_URL": "http://localhost:3000", "GITEA_ACCESS_TOKEN": "", "FILE_CACHE_DIR": "/tmp/gitea_mcp_cache", "FILE_CACHE_MAX_AGE": "3600"}}, "gitea-mcp-startup": {"command": "python", "args": ["start_enhanced_mcp.py"], "env": {"GITEA_URL": "http://localhost:3000", "GITEA_ACCESS_TOKEN": ""}}}}