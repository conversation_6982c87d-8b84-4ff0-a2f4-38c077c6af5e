{"mcpServers": {"gitea-mcp-stdio": {"command": "gitea-mcp", "args": ["-t", "stdio", "--host", "http://localhost:3000"], "env": {"GITEA_ACCESS_TOKEN": ""}}, "gitea-mcp-debug": {"command": "gitea-mcp", "args": ["-t", "stdio", "--host", "http://localhost:3000", "-d"], "env": {"GITEA_ACCESS_TOKEN": ""}}, "gitea-mcp-readonly": {"command": "gitea-mcp", "args": ["-t", "stdio", "--host", "http://localhost:3000", "--readonly"], "env": {"GITEA_ACCESS_TOKEN": ""}}, "gitea-mcp-insecure": {"command": "gitea-mcp", "args": ["-t", "stdio", "--host", "http://localhost:3000", "--insecure"], "env": {"GITEA_ACCESS_TOKEN": "", "GITEA_INSECURE": "true"}}}}